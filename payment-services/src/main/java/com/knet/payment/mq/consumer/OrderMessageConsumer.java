package com.knet.payment.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.OrderMessage;
import com.knet.common.enums.PaymentChannel;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.resp.CreatePaymentResponse;
import com.knet.payment.service.IPaymentService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.OrderServicesConstants.ORDER_CREATED;

/**
 * <AUTHOR>
 * @date 2025/6/10 17:41
 * @description: 订单消息消费者
 */
@Slf4j
@Component
public class OrderMessageConsumer {
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private IPaymentService paymentService;

    @RabbitListener(
            queues = "order-queue.payment-services",
            ackMode = "MANUAL" // 必须显式指定
    )
    public void handleOrderNotification(
            @Payload String messageBody,
            @Header("routingKey") String orderType,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if (ORDER_CREATED.equals(orderType)) {
                // 幂等性检查
                if (!redisCacheUtil.setIfAbsent(messageId, "PROCESSED", 60)) {
                    log.warn("重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                processOrder(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("订单处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * 创建支付订单
     *
     * @param messageBody 消息体
     */
    private void processOrder(String messageBody) {
        OrderMessage orderMessage = JSON.parseObject(messageBody, OrderMessage.class);
        log.info("支付服务处理订单创建消息: {}", messageBody);
        try {
            // 创建支付订单请求
            CreatePaymentRequest paymentRequest = CreatePaymentRequest
                    .builder()
                    .userId(orderMessage.getUserId())
                    .orderId(orderMessage.getOrderId())
                    .amount(orderMessage.getTotalAmount())
                    .paymentChannel(PaymentChannel.WALLET)
                    .build();
            // 创建支付订单
            CreatePaymentResponse paymentResponse = paymentService.createPayment(paymentRequest);
            log.info("支付订单创建成功: groupId={}, paymentId={}, orderId={}",
                    paymentResponse.getGroupId(), paymentResponse.getPaymentId(), orderMessage.getOrderId());
        } catch (Exception e) {
            log.error("创建支付订单失败: orderId={}, error={}", orderMessage.getOrderId(), e.getMessage());
            // TODO: 发送支付订单创建失败补偿事件，通知订单服务做补偿操作
            throw new ServiceException("创建支付订单失败: " + e.getMessage());
        }
    }
}
