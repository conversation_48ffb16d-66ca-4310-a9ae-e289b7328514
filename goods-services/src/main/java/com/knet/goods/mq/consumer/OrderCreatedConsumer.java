package com.knet.goods.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.dto.message.OrderMessage;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.service.IInventoryService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.OrderServicesConstants.ORDER_CREATED;

/**
 * <AUTHOR>
 * @date 2025/6/6 15:14
 * @description: 订单创建消费者
 */
@Slf4j
@Component
public class OrderCreatedConsumer {
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private IInventoryService iInventoryService;

    @DistributedLock(key = "'orderConsumer", expire = 2)
    @RabbitListener(
            queues = "order-queue.goods-services",
            ackMode = "MANUAL" // 必须显式指定
    )
    public void handleOrderNotification(
            @Payload String messageBody,
            @Header("routingKey") String orderType,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if (ORDER_CREATED.equals(orderType)) {
                // 幂等性检查
                if (!redisCacheUtil.setIfAbsent(messageId, "PROCESSED", 60)) {
                    log.warn("重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                // 业务处理
                processOrder(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("订单处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * todo 库存检查锁定
     *
     * @param messageBody 消息体
     */
    private void processOrder(String messageBody) {
        OrderMessage orderMessage = JSON.parseObject(messageBody, OrderMessage.class);
        log.info("业务逻辑: {}", messageBody);
        try {
            boolean inventory = iInventoryService.checkAndLockInventory(orderMessage.getOrderId());
            if (!inventory) {
                //todo 发送库存扣减失败补偿事件 通知支付、订单服务做补偿操作
            }
        } catch (Exception e) {
            //todo 发送库存扣减失败补偿事件 通知支付、订单服务做补偿操作
            throw new ServiceException("库存锁定失败");
        }
    }
}
